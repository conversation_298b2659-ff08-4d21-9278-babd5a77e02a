/**
 * Unified Cache Configuration
 *
 * Site-wide caching constants to ensure consistency across all page types:
 * - Homepage (tier articles)
 * - Article pages (single articles)
 * - Category pages (category articles)
 *
 * Following Next.js best practices and ensuring predictable performance.
 */

// ✅ Unified cache durations across all pages
export const CACHE_DURATIONS = {
  DEFAULT: 300, // 5 minutes - homepage tiers, category articles, article pages
  STATIC_CONTENT: 3600, // 1 hour - category info, site config, navigation
  SEARCH: 900, // 15 minutes - search results, related articles
  NAVIGATION: 1800, // 30 minutes - menu data, categories list
} as const;

// ✅ Unified cache tags for cross-page invalidation
export const CACHE_TAGS = {
  ARTICLES: 'articles',
  CATEGORIES: 'categories',
  PAGES: 'pages',
  TIERS: {
    TIER_1: 'tier-1',
    TIER_2: 'tier-2',
    TIER_3: 'tier-3',
  },
  NAVIGATION: 'navigation',
  SEARCH: 'search',
} as const;

// ✅ Field selection patterns for consistent data optimization
export const FIELD_SETS = {
  // Minimal fields for lists (category lists, related articles, homepage tiers)
  LIST_MINIMAL: {
    id: true,
    title: true,
    slug: true,
    publishedAt: true,
    createdAt: true,
    updatedAt: true, // ✅ Required field for Article type
    articleType: true, // ✅ Required field for Article type
    workflowStage: true,
    placement: true,
    trending: true,
    readTimeMinutes: true, // ✅ PHASE 2: Use pre-computed field
    pinned: true,
    categories: true, // Use true for full category info - PayloadCMS will handle the fields
    featuredImage: true,
    germanTab: {
      germanTitle: true,
      germanSummary: true,
    },
    englishTab: {
      enhancedTitle: true,
      enhancedSummary: true,
    },
  },

  // Hero fields for featured content (homepage hero, category hero)
  HERO_FEATURED: {
    id: true,
    title: true,
    slug: true,
    publishedAt: true,
    createdAt: true,
    updatedAt: true, // ✅ Required field for Article type
    articleType: true, // ✅ Required field for Article type
    workflowStage: true,
    placement: true,
    trending: true,
    readTimeMinutes: true, // ✅ PHASE 2: Use pre-computed field
    pinned: true,
    categories: true, // Full category info for hero articles
    featuredImage: true,
    germanTab: {
      germanTitle: true,
      germanSummary: true,
    },
    englishTab: {
      enhancedTitle: true,
      enhancedSummary: true,
    },
  },

  // ✅ Page-specific field sets for efficient caching
  PAGES: {
    // Basic page info for listings and navigation
    LIST_MINIMAL: {
      id: true,
      title: true,
      slug: true,
      publishedAt: true,
      createdAt: true,
      _status: true,
      // ❌ Excludes: content fields (germanContent, englishContent)
    },

    // Full page display with content - only use when needed
    FULL_DISPLAY: {
      id: true,
      title: true,
      slug: true,
      publishedAt: true,
      createdAt: true,
      _status: true,
      featuredImage: true,
      parent: true, // ✅ Include parent for breadcrumbs and hierarchical URLs
      enableBreadcrumbs: true, // ✅ Include breadcrumbs flag
      germanTab: {
        germanTitle: true,
        germanContent: true, // ✅ Include content for single page display
      },
      englishTab: {
        title: true,
        content: true, // ✅ Include content for single page display
      },
    },
  },
} as const;

// ✅ Cache key generators for consistent naming
export const generateCacheKey = {
  article: (slug: string) => `article-${slug}`,
  category: (slug: string) => `category-${slug}`,
  categoryArticles: (slug: string) => `category-articles-${slug}`,
  categoryLayout: (slug: string) => `category-layout-${slug}`,
  categoryExists: (slug: string) => `category-exists-${slug}`,
  page: (slug: string) => `page-${slug}`,
  pageExists: (slug: string) => `page-exists-${slug}`,
  tier: (tier: string) => `tier-${tier}`,
  relatedArticles: (articleId: string | number) =>
    `related-articles-${articleId}`,
} as const;
