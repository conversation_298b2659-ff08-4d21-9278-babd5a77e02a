import { unstable_cache } from 'next/cache';
import { getPayload } from 'payload';
import config from '@/payload.config';
import type { Page } from '@/payload-types';
import {
  CACHE_DURATIONS,
  CACHE_TAGS,
  generateCacheKey,
  FIELD_SETS,
} from './constants';

// ✅ Page fetching with smart field selection to prevent 2MB cache issues
const fetchPageBySlug = async (slug: string): Promise<Page | null> => {
  const payload = await getPayload({ config });

  const result = await payload.find({
    collection: 'pages',
    where: {
      and: [{ slug: { equals: slug } }, { _status: { equals: 'published' } }],
    },
    limit: 1,
    depth: 3, // ✅ Increased depth for parent hierarchy in breadcrumbs
    select: {
      ...FIELD_SETS.PAGES.FULL_DISPLAY,
      // ✅ Ensure parent relationship includes German tab data
      parent: {
        id: true,
        title: true,
        slug: true,
        _status: true,
        germanTab: {
          germanTitle: true,
        },
        englishTab: {
          title: true,
        },
        // ✅ Nested parent for grandparent breadcrumbs
        parent: {
          id: true,
          title: true,
          slug: true,
          _status: true,
          germanTab: {
            germanTitle: true,
          },
          englishTab: {
            title: true,
          },
        },
      },
    }, // ✅ Use field selection for performance with parent German data
  });

  return result.docs[0] || null;
};

// ✅ Cached page fetching with PayloadCMS utility tag format
export const getCachedPageBySlug = (slug: string) =>
  unstable_cache(
    async () => fetchPageBySlug(slug),
    ['pages', slug, 'depth-3'], // ✅ Updated depth key
    {
      revalidate: CACHE_DURATIONS.DEFAULT, // 5 minutes
      tags: [`pages_${slug}`], // ✅ Matches PayloadCMS utility tag format
    }
  );

// Cached published page slugs for static generation - follows articles pattern
export const getCachedPublishedPageSlugs = unstable_cache(
  async (): Promise<{ slug: string }[]> => {
    const payload = await getPayload({ config });

    const result = await payload.find({
      collection: 'pages',
      where: { _status: { equals: 'published' } },
      limit: 200, // Reasonable limit for pages
      depth: 0, // Only need slug field
      select: FIELD_SETS.PAGES.LIST_MINIMAL, // ✅ Use field selection for efficiency
    });

    return result.docs
      .filter(page => page.slug)
      .map(page => ({ slug: page.slug! }));
  },
  ['published-page-slugs'],
  {
    revalidate: CACHE_DURATIONS.DEFAULT,
    tags: [CACHE_TAGS.PAGES, 'published-page-slugs'], // ✅ Consistent tag naming
  }
);

// ✅ Check if page exists (for 404 handling) - follows articles pattern
export const getCachedPageExists = (slug: string) =>
  unstable_cache(
    async (): Promise<boolean> => {
      const payload = await getPayload({ config });

      const result = await payload.find({
        collection: 'pages',
        where: {
          and: [
            { slug: { equals: slug } },
            { _status: { equals: 'published' } },
          ],
        },
        limit: 1,
        depth: 0,
        select: { id: true }, // Only need to check existence
      });

      return result.docs.length > 0;
    },
    ['page-exists', slug],
    {
      revalidate: CACHE_DURATIONS.DEFAULT,
      tags: [`pages_${slug}`], // Same tag as full page for automatic invalidation
    }
  );
