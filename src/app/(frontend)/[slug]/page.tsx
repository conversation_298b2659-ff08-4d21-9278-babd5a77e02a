import type { <PERSON>ada<PERSON> } from 'next';
import { notFound } from 'next/navigation';
import { Suspense } from 'react';
import { draftMode } from 'next/headers';
import { getPayload } from 'payload';
import config from '@payload-config';
import {
  getCachedPageBySlug,
  getCachedPublishedPageSlugs,
} from '@/lib/cache/pages';
import type { Page, Media } from '@/payload-types';
import PageHeader from '@/components/pages/PageHeader';
import PageContent from '@/components/pages/PageContent';
import PageBreadcrumbs from '@/components/pages/PageBreadcrumbs';
import RecentArticlesSection from '@/components/pages/RecentArticlesSection';
import PageAccessibilityNav from '@/components/pages/PageAccessibilityNav';
import RelatedArticlesSkeleton from '@/components/articles/RelatedArticlesSkeleton';

interface PageProps {
  params: Promise<{
    slug: string;
  }>;
}

// Function to get page with draft mode support and parent relationships
async function getPage(slug: string, isDraftMode: boolean) {
  if (isDraftMode) {
    // Fetch draft version when in draft mode
    const payload = await getPayload({ config });
    const pages = await payload.find({
      collection: 'pages',
      where: { slug: { equals: slug } },
      draft: true,
      limit: 1,
      depth: 3, // Increased depth to get parent hierarchy
    });
    return pages.docs[0] || null;
  } else {
    // Use cached version for published pages
    const getCachedDoc = getCachedPageBySlug(slug);
    return await getCachedDoc();
  }
}

// Generate metadata for SEO with hierarchy support
export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params;
  const draft = await draftMode();
  const isDraftMode = draft.isEnabled;

  const page = await getPage(slug, false); // Use published for metadata consistency

  if (!page || (!isDraftMode && (page as any)._status !== 'published')) {
    return {
      title: 'Page Not Found | Börsen Blick',
      description: 'The requested page could not be found.',
    };
  }

  // Prioritise German content first, then English, then fallback
  const title =
    page.germanTab?.germanTitle || page.englishTab?.title || page.title;

  const description = 'Static page content from Börsen Blick.';

  // Handle featuredImage which can be number (ID) or Media object
  const featuredImage =
    typeof page.featuredImage === 'object'
      ? (page.featuredImage as Media)
      : null;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
      images: featuredImage?.url
        ? [
            {
              url: featuredImage.url,
              width: 1200,
              height: 630,
              alt: featuredImage.alt || title,
            },
          ]
        : [],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: featuredImage?.url ? [featuredImage.url] : [],
    },
  };
}

// Generate static params for published pages using cached data
export async function generateStaticParams(): Promise<{ slug: string }[]> {
  try {
    return await getCachedPublishedPageSlugs();
  } catch (error) {
    console.error('Error generating static params for pages:', error);
    return [];
  }
}

// Enable dynamic params for draft routes
export const dynamicParams = true;

// Main page component with hierarchical support
export default async function PageDisplay({ params }: PageProps) {
  const { slug } = await params;
  const draft = await draftMode();
  const isDraftMode = draft.isEnabled;

  const page = await getPage(slug, isDraftMode);

  // Check access: draft mode shows any page, normal mode only shows published pages
  if (!page) {
    notFound();
  }

  return (
    <div className="min-h-dvh bg-background">
      {/* Accessibility Navigation */}
      <PageAccessibilityNav />

      {/* Draft Mode Banner */}
      {isDraftMode && (
        <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-2">
          <div className="max-w-[1440px] mx-auto text-center">
            <span className="text-yellow-800 text-sm font-medium">
              🚧 Draft Mode: You are viewing unpublished content
            </span>
            <span className="mx-2 text-yellow-600">•</span>
            <a
              href={`/api/exit-preview?slug=/${slug}`}
              className="text-yellow-800 text-sm font-medium hover:text-yellow-900 underline"
            >
              Exit Draft Mode
            </a>
          </div>
        </div>
      )}

      {/* Main Content Grid */}
      <div className="max-w-[1440px] mx-auto px-4 py-8 lg:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-0">
          {/* Column 1: Reserved for future use */}
          <aside className="sm:border-r sm:border-border sm:pr-2 lg:pr-3 xl:pr-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-3 sm:order-1 hidden sm:block">
            {/* Reserved for future sidebar content */}
          </aside>

          {/* Columns 2-3: Page content */}
          <section
            id="page-content"
            className="sm:col-span-1 lg:col-span-2 xl:col-span-2 sm:border-r sm:border-border sm:px-2 lg:px-3 xl:px-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-1 sm:order-2"
            aria-labelledby="page-title"
            tabIndex={-1}
          >
            <div className="space-y-6 lg:space-y-8">
              {/* Breadcrumbs */}
              <PageBreadcrumbs page={page} locale="de" />

              {/* Page Header */}
              <PageHeader page={page} locale="de" />

              {/* Page Content */}
              <PageContent page={page} locale="de" />
            </div>
          </section>

          {/* Column 4: Recent articles sidebar */}
          <aside
            id="related-articles"
            className="xl:pl-4 order-2 sm:order-3"
            aria-labelledby="recent-articles-heading"
            tabIndex={-1}
          >
            <h2 id="recent-articles-heading" className="sr-only">
              Recent Articles
            </h2>
            <Suspense fallback={<RelatedArticlesSkeleton maxArticles={4} />}>
              <RecentArticlesSection maxArticles={4} locale="de" />
            </Suspense>
          </aside>
        </div>
      </div>
    </div>
  );
}
