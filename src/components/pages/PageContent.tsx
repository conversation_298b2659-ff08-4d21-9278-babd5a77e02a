import React from 'react';
import Image from 'next/image';
import type { Page, Media } from '@/payload-types';

interface PageContentProps {
  page: Page;
  locale?: 'de' | 'en';
}

// Comprehensive Lexical content renderer supporting all configured node types
function renderLexicalContent(lexicalData: any): React.ReactNode {
  if (!lexicalData || typeof lexicalData !== 'object') {
    return null;
  }

  const root = lexicalData.root;
  if (!root || !Array.isArray(root.children)) {
    return null;
  }

  const renderNode = (node: any, index: number): React.ReactNode => {
    if (!node || typeof node !== 'object') {
      return null;
    }

    switch (node.type) {
      case 'paragraph':
        return (
          <p key={index} className="mb-4 font-serif text-base/8">
            {Array.isArray(node.children)
              ? node.children.map((child: any, i: number) =>
                  renderNode(child, i)
                )
              : null}
          </p>
        );

      case 'heading':
        const tag = node.tag || 'h3';
        const headingClasses = {
          h1: 'text-2xl md:text-3xl font-bold mb-6 mt-8 text-gray-900 dark:text-gray-100',
          h2: 'text-xl md:text-2xl font-semibold mb-5 mt-7 text-gray-900 dark:text-gray-100',
          h3: 'text-lg md:text-xl font-semibold mb-4 mt-6 text-gray-900 dark:text-gray-100',
          h4: 'text-base md:text-lg font-medium mb-3 mt-5 text-gray-800 dark:text-gray-200',
          h5: 'text-sm md:text-base font-medium mb-3 mt-4 text-gray-800 dark:text-gray-200',
          h6: 'text-sm font-medium mb-2 mt-3 text-gray-800 dark:text-gray-200',
        };

        const className = `font-serif ${headingClasses[tag as keyof typeof headingClasses] || headingClasses.h3}`;
        const children = Array.isArray(node.children)
          ? node.children.map((child: any, i: number) => renderNode(child, i))
          : null;

        return React.createElement(tag, { key: index, className }, children);

      case 'text':
        let textElement: React.ReactNode = node.text || '';

        // Handle text formatting using Lexical format flags
        if (node.format && typeof node.format === 'number') {
          if (node.format & 1) {
            // Bold
            textElement = <strong>{textElement}</strong>;
          }
          if (node.format & 2) {
            // Italic
            textElement = <em>{textElement}</em>;
          }
          if (node.format & 4) {
            // Underline
            textElement = <u>{textElement}</u>;
          }
          if (node.format & 8) {
            // Strikethrough
            textElement = <del>{textElement}</del>;
          }
        }

        return <span key={index}>{textElement}</span>;

      case 'linebreak':
        return <br key={index} />;

      // List support (ordered and unordered)
      case 'list':
        const ListTag = node.listType === 'number' ? 'ol' : 'ul';
        const listClassName =
          node.listType === 'number'
            ? 'list-decimal list-inside mb-4 ml-4 space-y-2'
            : 'list-disc list-inside mb-4 ml-4 space-y-2';

        return (
          <ListTag key={index} className={listClassName}>
            {Array.isArray(node.children)
              ? node.children.map((child: any, i: number) =>
                  renderNode(child, i)
                )
              : null}
          </ListTag>
        );

      case 'listitem':
        return (
          <li
            key={index}
            className="font-serif text-base/7 text-gray-800 dark:text-gray-200"
          >
            {Array.isArray(node.children)
              ? node.children.map((child: any, i: number) =>
                  renderNode(child, i)
                )
              : null}
          </li>
        );

      // Blockquote support
      case 'quote':
        return (
          <blockquote
            key={index}
            className="border-l-4 border-[#B08D57] dark:border-[#D4AF37] pl-4 py-2 my-6 italic font-serif text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-r-lg"
          >
            {Array.isArray(node.children)
              ? node.children.map((child: any, i: number) =>
                  renderNode(child, i)
                )
              : null}
          </blockquote>
        );

      // Horizontal rule support
      case 'horizontalrule':
        return (
          <hr
            key={index}
            className="my-8 border-0 h-px bg-linear-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent"
          />
        );

      // Upload/Image support
      case 'upload':
        const uploadValue = node.value;
        if (!uploadValue || typeof uploadValue !== 'object') {
          return null;
        }

        // Handle both ID reference and full media object
        const mediaUrl =
          typeof uploadValue === 'object' && uploadValue.url
            ? uploadValue.url
            : null;

        const altText = uploadValue.alt || 'Page image';
        const caption = uploadValue.caption;

        if (!mediaUrl) {
          return null;
        }

        return (
          <figure key={index} className="my-6">
            <div className="overflow-hidden rounded-lg">
              <Image
                src={mediaUrl}
                alt={altText}
                width={800}
                height={400}
                className="w-full h-auto object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 800px"
              />
            </div>
            {caption && (
              <figcaption className="mt-2 text-sm text-gray-600 dark:text-gray-400 font-sans italic text-center">
                {caption}
              </figcaption>
            )}
          </figure>
        );

      default:
        // Enhanced fallback for unknown node types
        console.warn(`Unknown Lexical node type: ${node.type}`, node);

        // If it has children, try to render them
        if (Array.isArray(node.children)) {
          return (
            <div key={index} className="unknown-lexical-node">
              {node.children.map((child: any, i: number) =>
                renderNode(child, i)
              )}
            </div>
          );
        }

        // If it's a text-like node, try to render the text
        if (node.text && typeof node.text === 'string') {
          return <span key={index}>{node.text}</span>;
        }

        return null;
    }
  };

  return (
    <div className="lexical-content">
      {root.children.map((child: any, index: number) =>
        renderNode(child, index)
      )}
    </div>
  );
}

export default function PageContent({ page, locale = 'de' }: PageContentProps) {
  // Handle featuredImage which can be number (ID) or Media object
  const featuredImage =
    typeof page.featuredImage === 'object'
      ? (page.featuredImage as Media)
      : null;

  // Prioritise content: German → English → fallback
  const content =
    page.germanTab?.germanContent || page.englishTab?.content || null;

  // Get title for alt text fallback
  const title =
    page.germanTab?.germanTitle || page.englishTab?.title || page.title;

  return (
    <div className="space-y-6 lg:space-y-8">
      {/* Prominent hero image */}
      {featuredImage?.url && (
        <div className="overflow-hidden rounded-lg">
          <Image
            src={featuredImage.url}
            alt={featuredImage.alt || title}
            width={800}
            height={400}
            className="w-full h-auto object-cover"
            priority={true}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 800px"
          />
        </div>
      )}

      {/* Page content with proper typography */}
      <article className="prose prose-base max-w-none dark:prose-invert">
        <div className="font-serif text-gray-800 dark:text-gray-200 text-base/8">
          {content ? (
            renderLexicalContent(content)
          ) : (
            <div className="text-gray-500 dark:text-gray-400 italic font-sans">
              <p>Content is being processed and will be available soon.</p>
            </div>
          )}
        </div>
      </article>
    </div>
  );
}
